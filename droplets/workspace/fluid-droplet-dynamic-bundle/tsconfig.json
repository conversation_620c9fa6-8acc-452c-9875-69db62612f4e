{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/frontend/*"]
    },
    // Additional options to help with @rjsf/core compatibility
    "noImplicitAny": false,
    "strictNullChecks": true,
    "strictFunctionTypes": false,
    "noImplicitReturns": false,
    "noImplicitThis": false
  },
  "include": ["app/frontend/**/*.ts", "app/frontend/**/*.tsx", "app/frontend/types/**/*.d.ts"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
