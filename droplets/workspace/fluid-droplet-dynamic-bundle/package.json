{"name": "droplet-template", "type": "module", "private": true, "devDependencies": {"@types/lodash": "^4.17.20", "@types/node": "^22.13.10", "@types/react": "^19", "@types/react-dom": "^19", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "^5.7.3", "vite": "^5.4.19", "vite-plugin-ruby": "^5.1.1"}, "scripts": {"dev": "vite dev", "build": "tsc && vite build", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "packageManager": "yarn@4.7.0", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@radix-ui/react-slot": "^1.1.2", "@rjsf/core": "^5.24.8", "@rjsf/utils": "^5.24.8", "@rjsf/validator-ajv8": "^5.24.8", "@tailwindcss/vite": "^4.0.14", "@tanstack/react-table": "^8.21.2", "autoprefixer": "^10.x", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "esbuild": "^0.25.1", "lucide-react": "^0.482.0", "postcss": "^8.x", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.x", "tailwindcss-animate": "^1.0.7", "vite-plugin-full-reload": "^1.2.0"}}